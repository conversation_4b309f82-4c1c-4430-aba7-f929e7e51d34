#!/usr/bin/env python3
"""
Test script for BlendPro dependency system
Tests if all required dependencies can be imported from lib/ directory
"""

import sys
import os
from pathlib import Path

# Add lib directory to sys.path
addon_dir = Path(__file__).parent
lib_dir = addon_dir / "lib"

if lib_dir.exists():
    lib_path_str = str(lib_dir)
    if lib_path_str not in sys.path:
        sys.path.insert(0, lib_path_str)
        print(f"✓ Added lib directory to sys.path: {lib_path_str}")
else:
    print(f"✗ Lib directory not found: {lib_dir}")
    sys.exit(1)

# Test imports
def test_import(package_name, display_name):
    """Test importing a package"""
    try:
        module = __import__(package_name)
        version = getattr(module, '__version__', 'unknown')
        print(f"✓ {display_name}: {version}")
        return True
    except ImportError as e:
        print(f"✗ {display_name}: {e}")
        return False

def test_pil_image():
    """Test PIL.Image specifically"""
    try:
        from PIL import Image
        print(f"✓ PIL.Image: Available")
        return True
    except ImportError as e:
        print(f"✗ PIL.Image: {e}")
        return False

def main():
    """Run all dependency tests"""
    print("BlendPro Dependency Test")
    print("=" * 50)
    
    # Test core dependencies
    dependencies = [
        ('openai', 'OpenAI API Client'),
        ('PIL', 'Pillow (Image Processing)'),
        ('numpy', 'NumPy (Numerical Computing)'),
        ('requests', 'Requests (HTTP Library)'),
        ('json5', 'JSON5 Parser'),
        ('httpx', 'HTTPX (Async HTTP Client)'),
        ('aiohttp', 'AioHTTP (Async HTTP Server/Client)'),
        ('colorlog', 'ColorLog (Enhanced Logging)'),
        ('dateutil', 'Python DateUtil'),
        ('orjson', 'OrJSON (Fast JSON Parser)'),
    ]
    
    success_count = 0
    total_count = len(dependencies) + 1  # +1 for PIL.Image test
    
    # Test each dependency
    for package, display in dependencies:
        if test_import(package, display):
            success_count += 1
    
    # Test PIL.Image specifically
    if test_pil_image():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"Results: {success_count}/{total_count} dependencies available")
    
    if success_count == total_count:
        print("✓ All dependencies are working correctly!")
        return True
    else:
        print("✗ Some dependencies are missing or broken")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
