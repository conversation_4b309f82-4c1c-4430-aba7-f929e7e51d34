"""
Test script to verify BlendPro addon functionality in Blender
Run this in Blender's Text Editor or via command line
"""

import bpy
import sys
import os
from pathlib import Path

def test_blendpro_addon():
    """Test BlendPro addon registration and functionality"""
    
    print("=" * 60)
    print("BlendPro Addon Test")
    print("=" * 60)
    
    # Check if addon is enabled
    addon_name = "BlendProV2"
    if addon_name in bpy.context.preferences.addons:
        print(f"✓ {addon_name} addon is enabled")
    else:
        print(f"✗ {addon_name} addon is not enabled")
        return False
    
    # Test dependency loading
    try:
        # Add lib path
        addon_dir = Path(bpy.utils.user_resource('SCRIPTS', "addons")) / addon_name
        lib_dir = addon_dir / "lib"
        
        if lib_dir.exists():
            lib_path_str = str(lib_dir)
            if lib_path_str not in sys.path:
                sys.path.insert(0, lib_path_str)
                print(f"✓ Added lib directory: {lib_path_str}")
        
        # Test core imports
        import openai
        print(f"✓ OpenAI: {openai.__version__}")
        
        from PIL import Image
        print("✓ PIL.Image: Available")
        
        import numpy as np
        print(f"✓ NumPy: {np.__version__}")
        
    except Exception as e:
        print(f"✗ Dependency test failed: {e}")
        return False
    
    # Test BlendPro modules
    try:
        from BlendProV2.utils.dependency_loader import get_dependency_loader
        loader = get_dependency_loader()
        print("✓ Dependency loader initialized")
        
        from BlendProV2.config.settings import get_settings
        settings = get_settings()
        print("✓ Settings module loaded")
        
    except Exception as e:
        print(f"✗ BlendPro module test failed: {e}")
        return False
    
    print("=" * 60)
    print("✓ All tests passed! BlendPro is working correctly.")
    return True

if __name__ == "__main__":
    test_blendpro_addon()
