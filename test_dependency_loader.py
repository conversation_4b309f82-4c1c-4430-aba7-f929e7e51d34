"""
Test script for BlendPro Dependency Loader
Tests the dependency management system functionality

Author: inkbytefo
"""

import sys
import os

# Add the current directory to sys.path to import BlendPro modules
sys.path.insert(0, os.path.dirname(__file__))

def test_dependency_loader():
    """Test the dependency loader functionality"""
    print("=== BlendPro Dependency Loader Test ===\n")

    try:
        # Import dependency loader directly
        import importlib.util
        spec = importlib.util.spec_from_file_location("dependency_loader", "utils/dependency_loader.py")
        dependency_loader = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(dependency_loader)

        get_dependency_loader = dependency_loader.get_dependency_loader
        safe_import = dependency_loader.safe_import
        require_package = dependency_loader.require_package
        is_available = dependency_loader.is_available
        get_import_status = dependency_loader.get_import_status
        DependencyError = dependency_loader.DependencyError

        print("✓ Dependency loader imported successfully")
    except Exception as e:
        print(f"✗ Failed to import dependency loader: {e}")
        return False
    
    # Test 1: Safe import of available package
    print("\n1. Testing safe import of available packages:")
    
    # Test PIL (should be available in lib/)
    pil = safe_import('PIL', 'Pillow (Image Processing)', required=False)
    if pil:
        print("✓ PIL imported successfully")
        print(f"  Version: {getattr(pil, '__version__', 'unknown')}")
    else:
        print("✗ PIL import failed")
    
    # Test numpy (should be available in lib/)
    numpy = safe_import('numpy', 'NumPy (Numerical Computing)', required=False)
    if numpy:
        print("✓ NumPy imported successfully")
        print(f"  Version: {getattr(numpy, '__version__', 'unknown')}")
    else:
        print("✗ NumPy import failed")
    
    # Test 2: Safe import of unavailable package
    print("\n2. Testing safe import of unavailable package:")
    fake_package = safe_import('nonexistent_package', 'Fake Package', required=False)
    if fake_package is None:
        print("✓ Correctly handled unavailable package")
    else:
        print("✗ Should have returned None for unavailable package")
    
    # Test 3: Required package import (should work for OpenAI)
    print("\n3. Testing required package import:")
    try:
        openai = require_package('openai', 'OpenAI API Client')
        print("✓ OpenAI imported successfully as required package")
        print(f"  Version: {getattr(openai, '__version__', 'unknown')}")
    except DependencyError as e:
        print(f"✗ Required package import failed: {e}")
    
    # Test 4: Required package import failure
    print("\n4. Testing required package import failure:")
    try:
        fake_required = require_package('nonexistent_required', 'Fake Required Package')
        print("✗ Should have raised DependencyError")
    except DependencyError:
        print("✓ Correctly raised DependencyError for missing required package")
    
    # Test 5: Availability check
    print("\n5. Testing availability checks:")
    print(f"PIL available: {is_available('PIL')}")
    print(f"NumPy available: {is_available('numpy')}")
    print(f"OpenAI available: {is_available('openai')}")
    print(f"Fake package available: {is_available('nonexistent_package')}")
    
    # Test 6: Import status
    print("\n6. Testing import status:")
    status = get_import_status()
    print("Successful imports:")
    for package, info in status['successful_imports'].items():
        print(f"  {package}: {info['version']}")
    
    print("Failed imports:")
    for package, error in status['failed_imports'].items():
        print(f"  {package}: {error}")
    
    print("Feature flags:")
    for package, available in status['feature_flags'].items():
        print(f"  {package}: {'✓' if available else '✗'}")
    
    # Test 7: Version checking
    print("\n7. Testing version checking:")
    try:
        # Test with minimum version requirement
        numpy_versioned = safe_import('numpy', 'NumPy', required=False, min_version='1.20.0')
        if numpy_versioned:
            print("✓ NumPy meets version requirement")
        else:
            print("✗ NumPy version check failed")
    except Exception as e:
        print(f"✗ Version check error: {e}")
    
    print("\n=== Test Complete ===")
    return True

def test_api_client_integration():
    """Test API client integration with dependency loader"""
    print("\n=== API Client Integration Test ===\n")

    try:
        # Skip API client test as it requires Blender environment
        print("⚠ Skipping API Client test (requires Blender environment)")
        return True

    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_vision_integration():
    """Test vision module integration with dependency loader"""
    print("\n=== Vision Module Integration Test ===\n")

    try:
        # Skip vision test as it requires Blender environment
        print("⚠ Skipping Vision Module test (requires Blender environment)")
        return True

    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("BlendPro Dependency Management System Test")
    print("=" * 50)
    
    success = True
    
    # Run tests
    success &= test_dependency_loader()
    success &= test_api_client_integration()
    success &= test_vision_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    print("=" * 50)
